{"expo": {"name": "kali", "slug": "kali", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "kali", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "expo": {"scheme": "kali", "deepLinking": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "in.kali"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-font", {"fonts": ["node_modules/@expo-google-fonts/urbanist/400Regular/Urbanist_400Regular.ttf", "node_modules/@expo-google-fonts/urbanist/500Medium/Urbanist_500Medium.ttf", "node_modules/@expo-google-fonts/urbanist/600SemiBold/Urbanist_600SemiBold.ttf", "node_modules/@expo-google-fonts/urbanist/700Bold/Urbanist_700Bold.ttf", "node_modules/@expo-google-fonts/urbanist/800ExtraBold/Urbanist_800ExtraBold.ttf"]}], "@react-native-google-signin/google-signin", "expo-apple-authentication"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "304fd2c6-f889-45af-b8e8-448f8d3fed2d"}}, "owner": "kali-app"}}