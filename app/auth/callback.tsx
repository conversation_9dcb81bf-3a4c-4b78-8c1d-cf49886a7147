import { useEffect } from 'react';
import { useRouter } from 'expo-router';
import { supabase } from '@/lib/supabase';
import { toast } from '@/toast/toast';
import FullscreenLoader from '@/components/k-components/FullscreenLoader';

export default function AuthCallback() {
  const router = useRouter();

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        // Get the current session after OAuth redirect
        const { data: { session }, error } = await supabase.auth.getSession();
        
        if (error) {
          console.error('Auth callback error:', error);
          toast.error('Authentication failed');
          router.replace('/account/login');
          return;
        }

        if (session) {
          toast.success('Successfully signed in!');
          // Navigate back to the main app
          router.replace('/');
        } else {
          toast.error('No session found');
          router.replace('/account/login');
        }
      } catch (error) {
        console.error('Auth callback error:', error);
        toast.error('Authentication failed');
        router.replace('/account/login');
      }
    };

    handleAuthCallback();
  }, [router]);

  return <FullscreenLoader />;
}
