import React, { useEffect, useState } from 'react';
import { Platform } from 'react-native';
import * as AppleAuthentication from 'expo-apple-authentication';
import { useAppleLogin } from '@/hooks/social-login/useAppleLogin';
import { triggerHapticFeedback } from '@/utils';

interface AppleSignInButtonProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  variant?: 'login' | 'signup';
}

export function AppleSignInButton({
  onSuccess,
  onError,
}: AppleSignInButtonProps) {
  const { signInWithApple, isAppleAuthAvailable } = useAppleLogin();
  const [isAvailable, setIsAvailable] = useState(false);

  useEffect(() => {
    const checkAvailability = async () => {
      const available = await isAppleAuthAvailable();
      setIsAvailable(available);
    };

    checkAvailability();
  }, [isAppleAuthAvailable]);

  const handleAppleSignIn = async () => {
    triggerHapticFeedback();

    const result = await signInWithApple();

    if (result.success) {
      onSuccess?.();
    } else if (result.error) {
      onError?.(result.error);
    }
  };

  // Don't render on non-iOS platforms or if not available
  if (Platform.OS !== 'ios' || !isAvailable) {
    return null;
  }

  return (
    <AppleAuthentication.AppleAuthenticationButton
      buttonType={AppleAuthentication.AppleAuthenticationButtonType.SIGN_IN}
      buttonStyle={AppleAuthentication.AppleAuthenticationButtonStyle.BLACK}
      cornerRadius={8}
      style={{ width: '100%', height: 48 }}
      onPress={handleAppleSignIn}
    />
  );
}
