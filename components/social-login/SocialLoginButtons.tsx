import React from 'react';
import { VStack } from '@/components/ui/vstack';
import { GoogleSignInButton } from './GoogleSignInButton';
import { AppleSignInButton } from './AppleSignInButton';
import { SocialLoginDivider } from './SocialLoginDivider';
import { getAvailableSocialProviders } from '@/utils/social-login/social-auth-helpers';

interface SocialLoginButtonsProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
  disabled?: boolean;
  variant?: 'login' | 'signup';
  showDivider?: boolean;
  dividerText?: string;
}

export function SocialLoginButtons({
  onSuccess,
  onError,
  disabled = false,
  variant = 'login',
  showDivider = true,
  dividerText,
}: SocialLoginButtonsProps) {
  const availableProviders = getAvailableSocialProviders();

  // Don't render if no providers are available
  if (availableProviders.length === 0) {
    return null;
  }

  const hasGoogle = availableProviders.some((p) => p.id === 'google');
  const hasApple = availableProviders.some((p) => p.id === 'apple');

  return (
    <VStack className="w-full" space="md">
      {showDivider && <SocialLoginDivider text={dividerText} />}

      <VStack className="w-full" space="sm">
        {hasGoogle && (
          <GoogleSignInButton
            onSuccess={onSuccess}
            onError={onError}
            disabled={disabled}
          />
        )}

        {hasApple && (
          <AppleSignInButton
            onSuccess={onSuccess}
            onError={onError}
            disabled={disabled}
            variant={variant}
          />
        )}
      </VStack>
    </VStack>
  );
}
