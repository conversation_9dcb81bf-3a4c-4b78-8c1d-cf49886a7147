import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { HStack } from '@/components/ui/hstack';
import { Divider } from '@/components/ui/divider';

interface SocialLoginDividerProps {
  text?: string;
}

export function SocialLoginDivider({ 
  text = 'Or continue with' 
}: SocialLoginDividerProps) {
  return (
    <HStack className="items-center w-full py-4" space="md">
      <Divider className="flex-1" />
      <Text className="text-typography-500 font-urbanist text-sm px-2">
        {text}
      </Text>
      <Divider className="flex-1" />
    </HStack>
  );
}
