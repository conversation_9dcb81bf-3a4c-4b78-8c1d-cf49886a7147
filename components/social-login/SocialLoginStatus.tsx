import React from 'react';
import { Text } from '@/components/ui/text';
import { VStack } from '@/components/ui/vstack';
import { getAvailableSocialProviders } from '@/utils/social-login/social-auth-helpers';

export function SocialLoginStatus() {
  const providers = getAvailableSocialProviders();

  return (
    <VStack className="p-4 bg-gray-100 rounded-lg" space="sm">
      <Text className="font-urbanistBold text-lg">Social Login Status</Text>

      {providers.length === 0 ? (
        <Text className="text-typography-500">
          No social login providers are currently configured.
        </Text>
      ) : (
        <VStack space="xs">
          <Text className="text-typography-600">Available providers:</Text>
          {providers.map((provider) => (
            <Text key={provider.id} className="text-typography-700">
              • {provider.name} -{' '}
              {provider.isConfigured ? 'Configured' : 'Not configured'}
            </Text>
          ))}
        </VStack>
      )}

      <Text className="text-typography-500 text-sm mt-2">
        To configure social login, see docs/SOCIAL_LOGIN_SETUP.md
      </Text>
    </VStack>
  );
}
