// Social Login Components
export { SocialLoginButtons } from './SocialLoginButtons';
export { GoogleSignInButton } from './GoogleSignInButton';
export { AppleSignInButton } from './AppleSignInButton';
export { SocialLoginDivider } from './SocialLoginDivider';

// Social Login Hooks
export { useSocialLogin } from '@/hooks/social-login/useSocialLogin';
export { useGoogleLogin } from '@/hooks/social-login/useGoogleLogin';
export { useAppleLogin } from '@/hooks/social-login/useAppleLogin';

// Social Login Utils
export {
  initializeSocialLogin,
  getAvailableSocialProviders,
  getSocialLoginErrorMessage,
} from '@/utils/social-login/social-auth-helpers';
export { configureGoogleSignIn, isGoogleConfigured } from '@/utils/social-login/google-config';
export { isAppleSignInSupported, getAppleButtonStyle } from '@/utils/social-login/apple-config';

// Types
export type { SocialProvider, SocialLoginResult } from '@/hooks/social-login/useSocialLogin';
export type { GoogleLoginResult } from '@/hooks/social-login/useGoogleLogin';
export type { AppleLoginResult } from '@/hooks/social-login/useAppleLogin';
