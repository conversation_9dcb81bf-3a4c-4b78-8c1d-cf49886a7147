# Google Sign-In Troubleshooting Guide

## Common Issues and Solutions

### 1. "Google Sign-In not configured" Warning

**Problem**: <PERSON><PERSON><PERSON> shows warning about missing configuration.

**Solution**:
```bash
# Check your .env file exists and has the correct variable
cat .env

# Should contain:
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_web_client_id.apps.googleusercontent.com
```

### 2. "No ID token present!" Error

**Problem**: Google Sign-In completes but no ID token is received.

**Solutions**:
- Verify your web client ID is correct in `.env`
- Check Google Cloud Console OAuth configuration
- Ensure you're using the **web application** client ID, not Android/iOS

### 3. "Google Play Services not available" (Android)

**Problem**: Error on Android devices.

**Solutions**:
- Test on a device with Google Play Services
- Update Google Play Services on the device
- Use a physical device instead of emulator

### 4. Sign-In Button Not Responding

**Problem**: But<PERSON> doesn't trigger sign-in flow.

**Solutions**:
- Check console for configuration errors
- Verify `@react-native-google-signin/google-signin` plugin in app.json
- Restart development server after configuration changes

### 5. Supabase Authentication Errors

**Problem**: Google Sign-In works but Supabase auth fails.

**Solutions**:
- Verify Google provider is enabled in Supabase Dashboard
- Check Client ID and Secret in Supabase are correct
- Ensure all Client IDs are added to Supabase (Android, iOS, Web)

## Debug Steps

### 1. Check Configuration

```typescript
// Add this to your app to debug configuration
import { isGoogleConfigured } from '@/utils/social-login/google-config';

console.log('Google configured:', isGoogleConfigured());
console.log('Web Client ID:', process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID);
```

### 2. Test Basic Google Sign-In

Use the reference implementation in `components/social-login/SupabaseGoogleExample.tsx` to test basic functionality.

### 3. Check Network Requests

Monitor network requests in development tools to see if authentication requests are being made.

## Production Checklist

- [ ] Production SHA-1 fingerprint added to Google Console
- [ ] Production build tested
- [ ] Supabase nonce validation enabled
- [ ] All client IDs added to Supabase
- [ ] Environment variables set correctly

## Getting Help

1. Check the [Supabase documentation](https://supabase.com/docs/guides/auth/social-login/auth-google)
2. Review Google Cloud Console configuration
3. Test with the exact Supabase example code
4. Check Supabase Dashboard authentication logs
