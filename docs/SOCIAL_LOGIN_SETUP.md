# Google Sign-In Setup for Supabase

Complete setup guide following the official Supabase documentation.

## Prerequisites

1. A Google Cloud project
2. A Supabase project
3. React Native app with Expo

## Step 1: Google Cloud Console Setup

### Create OAuth Credentials

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Navigate to **Credentials** page
4. Click **Create Credentials** > **OAuth client ID**

### For Android Apps

1. Choose **Android** application type
2. Provide your app's **package name** (from app.json: `in.kali`)
3. Get your SHA-1 certificate fingerprint:

   ```bash
   # For development
   keytool -keystore ~/.android/debug.keystore -list -v
   # Password: android

   # For production
   keytool -keystore your-release-key.keystore -list -v
   ```

4. Add the SHA-1 fingerprint to Google Console
5. **Important**: Add BOTH development and production SHA-1 fingerprints

### For iOS Apps

1. Choose **iOS** application type
2. Provide your app's **Bundle ID** (from app.json: `in.kali`)
3. Add App Store ID and Team ID if published

### For Web (Required for Supabase)

1. Choose **Web application** type
2. Add authorized redirect URIs:
   ```
   https://your-project-ref.supabase.co/auth/v1/callback
   ```
3. **Save the Web Client ID** - you'll need this for your app

## Step 2: Configure Supabase

1. Go to your [Supabase Dashboard](https://supabase.com/dashboard)
2. Navigate to **Authentication** > **Providers**
3. Enable **Google** provider
4. Add your credentials:
   - **Client ID**: Your **web application** client ID from Google Console
   - **Client Secret**: Your **web application** client secret from Google Console
5. Add all your Google Client IDs (Android, iOS, Web) to the **Client IDs** field

## Step 3: Configure Your App

### Update Environment Variables

Copy `.env.example` to `.env` and add your web client ID:

```bash
cp .env.example .env
```

Edit `.env`:

```bash
# Only the web client ID is needed for Supabase
EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID=your_web_client_id.apps.googleusercontent.com
```

### App Configuration

Your `app.json` should already include:

```json
{
  "expo": {
    "plugins": ["@react-native-google-signin/google-signin"]
  }
}
```

## Step 4: Testing

### Development Testing

1. **Expo Go**: Google Sign-In works in Expo Go
2. **Development Build**: Recommended for full testing
3. **Physical Device**: Required for proper testing

### Test the Implementation

1. Start your app:

   ```bash
   npm run ios
   # or
   npm run android
   ```

2. Navigate to login/signup screen
3. Tap the Google Sign-In button
4. Complete the Google authentication flow
5. Verify you're signed into your app

## Step 5: Production Deployment

### Before Going Live

1. ✅ Add production SHA-1 fingerprint to Google Console
2. ✅ Test with production build
3. ✅ Verify Supabase configuration
4. ✅ Enable nonce validation in Supabase (recommended)

### Nonce Validation

For production security:

1. Go to Supabase Dashboard > Authentication > Providers > Google
2. Ensure "Skip Nonce Check" is **disabled**
3. This prevents replay attacks

## Apple Sign In Setup

### 1. Apple Developer Account Setup

1. Go to [Apple Developer Console](https://developer.apple.com/)
2. Navigate to Certificates, Identifiers & Profiles
3. Create or update your App ID to include "Sign In with Apple" capability
4. Create a Service ID for web authentication
5. Create a Key for client authentication

### 2. Configure Supabase

1. Go to your Supabase dashboard
2. Navigate to Authentication > Providers
3. Enable Apple provider
4. Add your Apple OAuth credentials:
   - **Client ID**: Your Service ID
   - **Client Secret**: Generate using your private key

### 3. iOS Configuration

Apple Sign In is automatically available on iOS devices with iOS 13+ when using `expo-apple-authentication`.

## Testing

### Development Testing

1. **Google Sign-In**: Works in Expo Go and development builds
2. **Apple Sign In**: Only works on physical iOS devices with iOS 13+

### Production Testing

Both providers work in production builds on their respective platforms.

## Troubleshooting

### Common Issues

1. **Google Sign-In not working**: Check that your web client ID is correct
2. **Apple Sign In not available**: Ensure you're testing on iOS 13+ device
3. **Supabase errors**: Verify your provider configuration in Supabase dashboard

### Debug Steps

1. Check console logs for detailed error messages
2. Verify your OAuth client IDs and secrets
3. Ensure your app's bundle ID matches your OAuth configuration
4. Test with a fresh app installation

## Security Notes

1. Never commit OAuth secrets to version control
2. Use environment variables for sensitive configuration
3. Regularly rotate your OAuth credentials
4. Monitor authentication logs in Supabase dashboard

## Next Steps

After configuration:

1. Test both providers on their respective platforms
2. Implement proper error handling for edge cases
3. Add analytics tracking for social login usage
4. Consider implementing account linking for existing users
