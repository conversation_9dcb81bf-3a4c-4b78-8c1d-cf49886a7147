import { useState } from 'react';
import { Platform } from 'react-native';
import { supabase } from '@/lib/supabase';
import { useAuthUser } from '@/hooks/useAuthUser';
import { useSplash } from '@/context/SplashContext';
import {
  handleAuthError,
  getAuthErrorMessage,
} from '@/utils/auth-error-handler';
import { toast } from '@/toast/toast';

// Conditional imports to avoid errors when not available
let AppleAuthentication: any = null;
let Crypto: any = null;

try {
  AppleAuthentication = require('expo-apple-authentication');
  Crypto = require('expo-crypto');
} catch (error) {
  console.warn('Apple Authentication or Crypto module not available:', error);
}

export interface AppleLoginResult {
  success: boolean;
  error?: string;
}

export function useAppleLogin() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setUser } = useAuthUser();
  const { resetSplash } = useSplash();

  const isAppleAuthAvailable = async (): Promise<boolean> => {
    if (Platform.OS !== 'ios' || !AppleAuthentication) {
      return false;
    }

    try {
      return await AppleAuthentication.isAvailableAsync();
    } catch {
      return false;
    }
  };

  const signInWithApple = async (): Promise<AppleLoginResult> => {
    setLoading(true);
    setError(null);

    try {
      // Check if Apple Authentication is available
      const isAvailable = await isAppleAuthAvailable();
      if (!isAvailable || !AppleAuthentication || !Crypto) {
        const errorMessage = 'Apple Sign In is not available on this device';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      // Generate a secure nonce for the request
      const nonce = Math.random().toString(36).substring(2, 10);
      const hashedNonce = await Crypto.digestStringAsync(
        Crypto.CryptoDigestAlgorithm.SHA256,
        nonce,
        { encoding: Crypto.CryptoEncoding.BASE64URL }
      );

      // Request Apple ID credential
      const credential = await AppleAuthentication.signInAsync({
        requestedScopes: [
          AppleAuthentication.AppleAuthenticationScope.FULL_NAME,
          AppleAuthentication.AppleAuthenticationScope.EMAIL,
        ],
        nonce: hashedNonce,
      });

      if (!credential.identityToken) {
        const errorMessage = 'Failed to get Apple ID token';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      // Sign in to Supabase with the Apple ID token
      const { data, error: signInError } =
        await supabase.auth.signInWithIdToken({
          provider: 'apple',
          token: credential.identityToken,
          nonce,
        });

      if (signInError) {
        await handleAuthError(signInError, false);
        const errorMessage = signInError.code
          ? getAuthErrorMessage(signInError.code)
          : signInError.message || 'Something went wrong';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      if (data.user) {
        setUser(data.user);
        resetSplash();
        toast.success('Successfully signed in with Apple!');
      }

      return { success: true };
    } catch (err: any) {
      let errorMessage = 'An unexpected error occurred';

      if (err.code === 'ERR_REQUEST_CANCELED') {
        // User cancelled the Apple Sign In
        return { success: false, error: 'Sign-in cancelled' };
      }

      if (err.code === 'ERR_INVALID_RESPONSE') {
        errorMessage = 'Invalid response from Apple';
      } else if (err.code === 'ERR_REQUEST_FAILED') {
        errorMessage = 'Apple Sign In request failed';
      } else {
        errorMessage = 'Apple Sign In failed';
      }

      await handleAuthError(err, false);
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  return {
    loading,
    error,
    setError,
    signInWithApple,
    isAppleAuthAvailable,
  };
}
