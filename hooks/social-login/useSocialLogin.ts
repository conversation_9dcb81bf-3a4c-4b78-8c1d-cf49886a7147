import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { useAuthUser } from '@/hooks/useAuthUser';
import { useSplash } from '@/context/SplashContext';
import {
  handleAuthError,
  getAuthErrorMessage,
} from '@/utils/auth-error-handler';
import { toast } from '@/toast/toast';

export type SocialProvider = 'google' | 'apple';

export interface SocialLoginResult {
  success: boolean;
  error?: string;
}

export function useSocialLogin() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { setUser } = useAuthUser();
  const { resetSplash } = useSplash();

  const signInWithProvider = async (
    provider: SocialProvider,
    options?: {
      redirectTo?: string;
      scopes?: string;
      queryParams?: Record<string, string>;
    }
  ): Promise<SocialLoginResult> => {
    setLoading(true);
    setError(null);

    try {
      const { data, error: signInError } = await supabase.auth.signInWithOAuth({
        provider,
        options: {
          redirectTo: options?.redirectTo,
          scopes: options?.scopes,
          queryParams: options?.queryParams,
        },
      });

      if (signInError) {
        await handleAuthError(signInError, false);
        const errorMessage = signInError.code
          ? getAuthErrorMessage(signInError.code)
          : signInError.message || 'Something went wrong';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      // For OAuth flows, the user will be set via the auth state change listener
      // when they return from the OAuth provider
      return { success: true };
    } catch (err: any) {
      await handleAuthError(err, false);
      const errorMessage = 'An unexpected error occurred';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLoginSuccess = (user: any) => {
    setUser(user);
    resetSplash();
    toast.success('Successfully signed in!');
  };

  const handleSocialLoginError = (error: string) => {
    setError(error);
    toast.error(error);
  };

  return {
    loading,
    error,
    setError,
    signInWithProvider,
    handleSocialLoginSuccess,
    handleSocialLoginError,
  };
}
