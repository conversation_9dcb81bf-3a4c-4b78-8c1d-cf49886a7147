#!/bin/bash

# Google Sign-In Setup Script for Supabase
# This script helps you set up Google Sign-In following Supabase documentation

echo "🚀 Google Sign-In Setup for Supabase"
echo "======================================"

# Check if .env exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created"
else
    echo "✅ .env file already exists"
fi

# Check if Google Web Client ID is set
if grep -q "your_web_client_id" .env; then
    echo ""
    echo "⚠️  You need to configure your Google Web Client ID"
    echo ""
    echo "📋 Next steps:"
    echo "1. Go to Google Cloud Console: https://console.cloud.google.com/"
    echo "2. Create OAuth 2.0 credentials (Web application type)"
    echo "3. Copy your Web Client ID"
    echo "4. Edit .env and replace 'your_web_client_id.apps.googleusercontent.com'"
    echo ""
    echo "📖 Full setup guide: docs/SOCIAL_LOGIN_SETUP.md"
else
    echo "✅ Google Web Client ID appears to be configured"
fi

# Check if dependencies are installed
echo ""
echo "🔍 Checking dependencies..."

if grep -q "@react-native-google-signin/google-signin" package.json; then
    echo "✅ @react-native-google-signin/google-signin is installed"
else
    echo "❌ @react-native-google-signin/google-signin is missing"
    echo "   Run: npm install @react-native-google-signin/google-signin"
fi

# Check app.json configuration
if grep -q "@react-native-google-signin/google-signin" app.json; then
    echo "✅ Google Sign-In plugin configured in app.json"
else
    echo "❌ Google Sign-In plugin missing from app.json"
    echo "   Add '@react-native-google-signin/google-signin' to plugins array"
fi

echo ""
echo "🎯 Quick Test:"
echo "1. Configure your .env file with Google Web Client ID"
echo "2. Run: npm run ios (or npm run android)"
echo "3. Navigate to login screen and test Google Sign-In"
echo ""
echo "📚 Documentation:"
echo "- Setup Guide: docs/SOCIAL_LOGIN_SETUP.md"
echo "- Troubleshooting: docs/GOOGLE_SIGNIN_TROUBLESHOOTING.md"
echo ""
echo "🔗 Useful Links:"
echo "- Google Cloud Console: https://console.cloud.google.com/"
echo "- Supabase Dashboard: https://supabase.com/dashboard"
echo "- Supabase Docs: https://supabase.com/docs/guides/auth/social-login/auth-google"
