import { Platform } from 'react-native';

// Apple OAuth configuration
export const APPLE_CONFIG = {
  // Apple Sign In is automatically configured through Expo
  // No additional configuration needed for basic setup
  requestedScopes: ['fullName', 'email'],
  buttonType: 'signIn' as const,
  buttonStyle: 'black' as const,
  cornerRadius: 8,
};

export const isAppleSignInSupported = (): boolean => {
  return Platform.OS === 'ios';
};

export const getAppleButtonStyle = (theme: 'light' | 'dark' = 'light') => {
  return {
    ...APPLE_CONFIG,
    buttonStyle: theme === 'dark' ? 'white' : 'black',
  };
};
