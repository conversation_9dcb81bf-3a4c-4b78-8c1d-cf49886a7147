import { GoogleSignin } from '@react-native-google-signin/google-signin';

/**
 * Google Sign-In Configuration for Supabase
 * Following the official Supabase documentation:
 * https://supabase.com/docs/guides/auth/social-login/auth-google
 */

// Get your web client ID from Google Cloud Console
const GOOGLE_WEB_CLIENT_ID = process.env.EXPO_PUBLIC_GOOGLE_WEB_CLIENT_ID || '';

export const configureGoogleSignIn = () => {
  GoogleSignin.configure({
    scopes: ['https://www.googleapis.com/auth/drive.readonly'],
    webClientId: GOOGLE_WEB_CLIENT_ID,
  });
};

export const isGoogleConfigured = (): boolean => {
  return Boolean(GOOGLE_WEB_CLIENT_ID);
};
